package com.ruoyi.project.student.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.project.scenario.domain.DcCourseChatHistory;
import com.ruoyi.project.scenario.mapper.DcCourseChatHistoryMapper;
import com.ruoyi.project.student.websocket.ChatMessage;
import com.ruoyi.project.student.dto.GroupUserDTO;
import com.ruoyi.project.student.mapper.StSysUserMapper;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
@Slf4j
public class StChatGroupService {
    
    private final StSysUserMapper stSysUserMapper;
    private final DcCourseChatHistoryMapper dcCourseChatHistoryMapper;
    private final ObjectMapper objectMapper;
    private final IPuppetIconService puppetIconService;

    /**
     * 分页获取群组聊天消息
     *
     * @param groupId  群组ID
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 聊天消息列表
     */
    public List<ChatMessage> getGroupMessages(String groupId, Integer pageNum, Integer pageSize) {
        List<ChatMessage> messages = new ArrayList<>();
        
        try {
            // 创建分页对象
            Page<DcCourseChatHistory> page = new Page<>(pageNum, pageSize);

            // 排除的类型
            List<String> excludeTypeList = Stream.of("LEAVE", "JOIN")
                    .map(str -> "\"type\":\"" + str)
                    .toList();

            // 构建查询条件
            LambdaQueryWrapper<DcCourseChatHistory> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DcCourseChatHistory::getGroupId, groupId)
                       .orderByDesc(DcCourseChatHistory::getChatTime); // 按聊天时间降序排列
            excludeTypeList.forEach(type -> queryWrapper.notLike(DcCourseChatHistory::getChatContent, type));
            
            // 执行分页查询
            IPage<DcCourseChatHistory> chatHistoryPage = dcCourseChatHistoryMapper.selectPage(page, queryWrapper);
            
            // 转换数据
            for (DcCourseChatHistory history : chatHistoryPage.getRecords()) {
                try {
                    // 反序列化聊天内容 JSON
                    ChatMessage chatMessage = objectMapper.readValue(history.getChatContent(), ChatMessage.class);
                    
                    // 如果反序列化的消息中缺少一些基本信息，从数据库记录中补充
                    if (chatMessage.getGroupId() == null || chatMessage.getGroupId().isEmpty()) {
                        chatMessage.setGroupId(history.getGroupId());
                    }
                    if (chatMessage.getCourseId() == null || chatMessage.getCourseId().isEmpty()) {
                        chatMessage.setCourseId(history.getCourseId());
                    }
                    
                    // 设置时间戳（转换为13位时间戳）
                    if (history.getChatTime() != null) {
                        // 将数据库中的Date转换为13位时间戳
                        long timestamp = history.getChatTime().getTime();
                        chatMessage.setTimestamp(String.valueOf(timestamp));
                    }
                    
                    messages.add(chatMessage);
                } catch (Exception e) {
                    log.error("反序列化聊天消息失败，chatId: {}, chatContent: {}", 
                             history.getChatId(), history.getChatContent(), e);
                    // 继续处理其他消息，不因为单条消息解析失败而中断
                }
            }
            
            log.info("成功查询群组 [{}] 聊天记录，第 {} 页，每页 {} 条，共获取 {} 条消息", 
                     groupId, pageNum, pageSize, messages.size());
                     
        } catch (Exception e) {
            log.error("查询群组聊天消息失败，groupId: {}, pageNum: {}, pageSize: {}", 
                     groupId, pageNum, pageSize, e);
            // 发生异常时返回空列表，而不是抛出异常，保证接口的稳定性
        }
        
        return messages;
    }

    /**
     * 根据群组ID获取群组内的用户信息（包含马甲信息）
     * 支持课程群和分组群两种类型
     *
     * @param groupId 群组ID（可能是课程ID或分组ID）
     * @return 用户信息列表
     */
    public List<GroupUserDTO> getGroupUsers(String groupId) {
        if (groupId == null || groupId.trim().isEmpty()) {
            log.warn("查询群组用户信息失败：groupId 不能为空");
            return new ArrayList<>();
        }

        List<GroupUserDTO> users = new ArrayList<>();

        // 首先尝试作为分组ID查询
        users = stSysUserMapper.selectGroupUsersByGroupId(groupId);

        if (users != null && !users.isEmpty()) {
            // 找到了分组用户，说明这是一个分组群
            log.info("查询到课程分组 [{}] 用户 {} 人", groupId, users.size());
        } else {
            // 没有找到分组用户，尝试作为课程ID查询课程群用户
            // 首先尝试查询通过DC_COURSE_STUDENT表关联的学生
            users = stSysUserMapper.selectCourseUsersByCourseId(groupId);
            if (users != null && !users.isEmpty()) {
                log.info("查询到课程群 [{}] 用户 {} 人（通过DC_COURSE_STUDENT表）", groupId, users.size());
            } else {
                // 如果没有找到，再尝试查询通过班级代码关联的学生
                users = stSysUserMapper.selectCourseUsersByClassCode(groupId);
                if (users != null && !users.isEmpty()) {
                    log.info("查询到课程群 [{}] 用户 {} 人（通过班级代码关联）", groupId, users.size());
                } else {
                    log.warn("未找到群组 [{}] 的用户信息", groupId);
                    users = new ArrayList<>();
                }
            }
        }

        // 处理马甲图标URL拼接
        if (users != null && !users.isEmpty()) {
            for (GroupUserDTO user : users) {
                processPuppetIcon(user);
            }
        }

        return users;
    }
    
    /**
     * 处理马甲图标URL拼接
     * 
     * @param user 分组用户信息
     */
    private void processPuppetIcon(GroupUserDTO user) {
        try {
            String puppetIcon = user.getPuppetIcon();
            if (StringUtils.isNotEmpty(puppetIcon)) {
                // 拼接完整的马甲图标URL
                String iconUrl = puppetIconService.buildPuppetIconUrlSafely(puppetIcon);
                user.setPuppetIcon(iconUrl);
                log.debug("[StChatGroupService] 马甲图标URL拼接完成: {} -> {}", puppetIcon, iconUrl);
            }
        } catch (Exception e) {
            log.error("[StChatGroupService] 处理马甲图标URL失败", e);
            // 不影响主流程，继续执行
        }
    }
} 