<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.student.mapper.StSysUserMapper">
    
    <select id="selectStudentCourseInfo" resultType="com.ruoyi.project.student.domain.dto.StudentCourseInfoDTO">
        SELECT
            u.USER_NAME AS userName,
            u.NICK_NAME AS nickName,
            u.AVATAR AS avatar,
            c_group.GROUP_ID AS groupId,
            c_group.GROUP_NAME AS groupName,
            puppet.PUPPET_NAME AS puppetName,
            puppet.PUPPET_ICON AS puppetIcon,
            course.COURSE_ID AS courseId,
            course.COURSE_NAME AS courseName,
            course.COURSE_INTRODUCTION AS courseIntroduction,
            course.SCENE_ID AS sceneId,
            scene.SCENE_NAME AS sceneName,
            scene.SCENE_INTRODUCTION AS sceneIntroduction,
            CASE
                WHEN c_student.IS_GROUP_LEADER = 1 THEN 1
                ELSE 0
            END AS isGroupLeader
        FROM
            SYS_USER u
        LEFT JOIN DC_COURSE_STUDENT c_student
            ON u.USER_NAME = c_student.STUDENT_CODE
        LEFT JOIN DC_COURSE_PUPPET puppet
            ON c_student.PUPPET_ID = puppet.PUPPET_ID
        LEFT JOIN DC_COURSE_GROUP c_group
            ON puppet.GROUP_ID = c_group.GROUP_ID
        LEFT JOIN DC_COURSE course
            ON c_group.COURSE_ID = course.COURSE_ID
        LEFT JOIN DC_SCENE scene
            ON course.SCENE_ID = scene.SCENE_ID
        WHERE
            <if test="userName != null and userName != ''">
                u.USER_NAME = #{userName}
            </if>
            <if test="userId != null">
                <if test="userName != null and userName != ''">OR</if>
                u.USER_ID = #{userId}
            </if>
    </select>
    
    <select id="selectStudentCourseGroupInfo" resultType="com.ruoyi.project.student.domain.dto.StudentCourseInfoDTO">
        SELECT
            u.USER_NAME AS userName,
            u.NICK_NAME AS nickName,
            u.AVATAR AS avatar,
            course.COURSE_ID AS groupId,
            course.COURSE_NAME AS groupName,
            '' AS puppetName,
            '' AS puppetIcon,
            course.COURSE_ID AS courseId,
            course.COURSE_NAME AS courseName,
            course.COURSE_INTRODUCTION AS courseIntroduction,
            course.SCENE_ID AS sceneId,
            scene.SCENE_NAME AS sceneName,
            scene.SCENE_INTRODUCTION AS sceneIntroduction,
            1 AS isPublic,
            0 AS isGroupLeader
        FROM
            SYS_USER u
        LEFT JOIN ST_STUDENT_INFO stu_info
            ON u.USER_NAME = stu_info.STUDENT_CODE
        LEFT JOIN DC_COURSE course
            ON stu_info.STUDENT_CLASSCODE = course.CLASS_CODE
        LEFT JOIN DC_SCENE scene
            ON course.SCENE_ID = scene.SCENE_ID
        WHERE
            course.ISDELETE != '1'
            AND (
                <if test="userName != null and userName != ''">
                    u.USER_NAME = #{userName}
                </if>
                <if test="userId != null">
                    <if test="userName != null and userName != ''">OR</if>
                    u.USER_ID = #{userId}
                </if>
            )
    </select>

    <select id="selectGroupUsersByGroupId" resultType="com.ruoyi.project.student.dto.GroupUserDTO">
        SELECT
            su.user_id AS userId,
            su.user_name AS userName,
            su.nick_name AS nickName,
            su.avatar AS avatar,
            puppet.PUPPET_NAME AS puppetName,
            puppet.PUPPET_ICON AS puppetIcon,
            puppet.PUPPET_INDEX AS puppetIndex,
            dcs.IS_GROUP_LEADER as isGroupLeader
        FROM
            DC_COURSE_STUDENT dcs
        LEFT JOIN
            DC_COURSE_PUPPET puppet ON dcs.PUPPET_ID = puppet.PUPPET_ID
        LEFT JOIN
            SYS_USER su ON dcs.STUDENT_CODE = su.USER_NAME
        WHERE
            puppet.GROUP_ID = #{groupId}
            AND su.status = '0'
            AND su.del_flag = '0'
        ORDER BY
            CAST(puppet.PUPPET_INDEX AS NUMBER) ASC NULLS LAST,
            dcs.IS_GROUP_LEADER DESC,
            su.user_name ASC
    </select>

    <select id="selectCourseUsersByCourseId" resultType="com.ruoyi.project.student.dto.GroupUserDTO">
        SELECT
            su.user_id AS userId,
            su.user_name AS userName,
            su.nick_name AS nickName,
            su.avatar AS avatar,
            puppet.PUPPET_NAME AS puppetName,
            puppet.PUPPET_ICON AS puppetIcon,
            puppet.PUPPET_INDEX AS puppetIndex,
            dcs.IS_GROUP_LEADER as isGroupLeader
        FROM
            DC_COURSE_STUDENT dcs
        LEFT JOIN
            DC_COURSE_PUPPET puppet ON dcs.PUPPET_ID = puppet.PUPPET_ID
        LEFT JOIN
            DC_COURSE_GROUP cg ON puppet.GROUP_ID = cg.GROUP_ID
        LEFT JOIN
            SYS_USER su ON dcs.STUDENT_CODE = su.USER_NAME
        WHERE
            cg.COURSE_ID = #{courseId}
            AND su.status = '0'
            AND su.del_flag = '0'
        ORDER BY
            CAST(puppet.PUPPET_INDEX AS NUMBER) ASC NULLS LAST,
            dcs.IS_GROUP_LEADER DESC,
            su.user_name ASC
    </select>

    <select id="selectCourseUsersByClassCode" resultType="com.ruoyi.project.student.dto.GroupUserDTO">
        SELECT
            su.user_id AS userId,
            su.user_name AS userName,
            su.nick_name AS nickName,
            su.avatar AS avatar,
            '' AS puppetName,
            '' AS puppetIcon,
            '' AS puppetIndex,
            0 as isGroupLeader
        FROM
            SYS_USER su
        LEFT JOIN
            ST_STUDENT_INFO stu_info ON su.USER_NAME = stu_info.STUDENT_CODE
        LEFT JOIN
            DC_COURSE course ON stu_info.STUDENT_CLASSCODE = course.CLASS_CODE
        WHERE
            course.COURSE_ID = #{courseId}
            AND course.ISDELETE != '1'
            AND su.status = '0'
            AND su.del_flag = '0'
        ORDER BY
            su.user_name ASC
    </select>

</mapper>