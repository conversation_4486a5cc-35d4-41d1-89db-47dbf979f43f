<template>
  <view>
    <!-- 悬浮按钮 -->
    <wd-fab 
      v-if="!showPopup && currentGroupId !== 'public'"
      position="left-bottom" 
      :expandable="false"
      :gap="fabGap"
    >
      <template #trigger>
        <view class="survey-fab-trigger" @click="showSurveyList">
          <wd-icon name="notification" size="18" color="#fff"></wd-icon>
        </view>
      </template>
    </wd-fab>

    <!-- 问卷列表弹窗 -->
    <wd-popup 
      v-model="showPopup" 
      position="left" 
      @close="handlePopupClose"
      @opened="handlePopupOpened"
    >
      <view class="survey-popup-container">
        <!-- 标题栏 -->
        <view class="survey-header" :style="{ paddingTop: headerPaddingTop + 'px' }">
          <view class="survey-title-wrapper">
            <view class="survey-title">{{ courseName }}</view>
          </view>
          <view class="refresh-btn" :class="{ 'refreshing': isRefreshing }" @click="refreshSurveyList">
            <wd-icon 
              name="refresh" 
              size="20" 
              color="#666" 
            />
          </view>
        </view>
        
        <!-- 课程描述信息 -->
        <view v-if="courseIntroduction" class="course-description-section">
          <view class="course-description" :class="{ 'expanded': showFullDescription }" @click="toggleDescription">
            <text class="description-text">{{ courseIntroduction }}</text>
          </view>
          <!-- 分隔区域 -->
          <view class="section-divider"></view>
        </view>

        <!-- 内容滚动区域 -->
        <view class="survey-content-scroll">
          <!-- 阶段列表 -->
          <view class="stage-list-container" v-if="stageList.length > 0 || isLoadingStages">
            <!-- 加载状态 -->
            <view v-if="isLoadingStages" class="loading-state">
              <text class="loading-text">加载阶段中...</text>
            </view>
            
            <!-- 阶段列表 -->
            <view v-else class="stage-timeline">
              <view 
                class="stage-item" 
                v-for="(stage, index) in stageList" 
                :key="stage.courseStageId"
              >
                <!-- Timeline线条和圆点 -->
                <view class="timeline-wrapper">
                  <view 
                    class="timeline-dot" 
                    :class="{ 'active': index === 0 }"
                  ></view>
                  <view 
                    v-if="index < stageList.length - 1" 
                    class="timeline-line"
                  ></view>
                </view>
                
                <!-- 阶段内容 -->
                <view class="stage-content">
                  <text class="stage-title">{{ stage.courseStageTitle || `阶段${stage.courseStageOrder || index + 1}` }}</text>
                  <text v-if="stage.courseStageText" class="stage-desc">{{ stage.courseStageText }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 分隔区域 -->
          <view class="section-divider" v-if="stageList.length > 0 && (taskList.length > 0 || surveyList.length > 0)"></view>

          <!-- 任务列表 -->
          <view 
            class="task-list-container"
            v-if="taskList.length > 0 || isLoadingTasks"
          >
            <!-- 加载状态 -->
            <view v-if="isLoadingTasks" class="loading-state">
              <text class="loading-text">加载任务中...</text>
            </view>
            
            <!-- 任务列表 -->
            <view v-else>
              <view 
                class="task-item" 
                :class="{ 'task-replied': item.statusDesc === '已回复' }"
                v-for="item in taskList" 
                :key="item.thId" 
                @click="navigateToTask(item.thId)"
              >
                <view class="task-icon-wrapper">
                  <wd-icon name="edit" size="16" color="#FF6B35" />
                </view>
                <view class="task-info">
                  <text class="task-name">{{ item.taskTitle }}</text>
                </view>
                <view class="task-status">
                  <!-- 已回复状态图标 -->
                  <wd-icon 
                    v-if="item.statusDesc === '已回复'" 
                    name="check-circle" 
                    size="20" 
                    color="#52c41a" 
                    class="replied-icon"
                  />
                  <!-- 权限提示图标 -->
                  <wd-icon 
                    v-else-if="!userStore.isGroupLeader(props.currentGroupId)" 
                    name="lock" 
                    size="16" 
                    color="#999" 
                    class="lock-icon"
                  />
                  <wd-icon name="arrow-right" size="16" color="#ccc" />
                </view>
              </view>
              
              <!-- 空状态 -->
              <view v-if="taskList.length === 0 && !isLoadingTasks" class="empty-state">
                <text class="empty-text">暂无任务</text>
              </view>
            </view>
          </view>

          <!-- 分隔区域 -->
          <view class="section-divider" v-if="(stageList.length > 0 || taskList.length > 0) && surveyList.length > 0"></view>

          <!-- 问卷列表 -->
          <view 
            class="survey-list-container"
          >
            <view class="survey-item" v-for="item in surveyList" :key="item.paperId" @click="navigateToSurvey(item.paperId)">
              <view class="survey-icon-wrapper">
                <wd-icon name="file" size="16" color="#BD0407" ></wd-icon>
              </view>
              <view class="survey-info">
                <text class="survey-name">{{ item.paperTitle }}</text>
              </view>
              <view class="survey-status">
                <!-- 已提交状态图标 -->
                <wd-icon 
                  v-if="item.hasSubmitted" 
                  name="check-circle" 
                  size="20" 
                  color="#52c41a" 
                  class="submitted-icon"
                />
                <wd-icon name="arrow-right" size="16" color="#ccc" />
              </view>
            </view>
            
            <!-- 空状态 -->
            <view v-if="surveyList.length === 0 && !isLoading" class="empty-state">
              <text class="empty-text">暂无问卷</text>
            </view>
            
            <!-- 加载状态 -->
            <view v-if="isLoading" class="loading-state">
              <text class="loading-text">加载中...</text>
            </view>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
// 引入网络请求工具
import { get } from '@/utils/request'
// 引入用户store
import { useUserStore } from '@/stores/user'

// 接收父组件传递的 props
const props = defineProps({
  currentSceneId: {
    type: String,
    default: ''
  },
  currentCourseId: {
    type: String,
    default: ''
  },
  currentGroupId: {
    type: String,
    default: ''
  }
})

// 响应式数据
const showPopup = ref(false)
const surveyList = ref([])
const stageList = ref([])
const taskList = ref([])
const statusBarHeight = ref(0)
const headerPaddingTop = ref(20)
const isRefreshing = ref(false)
const isLoading = ref(false)
const isLoadingStages = ref(false)
const isLoadingTasks = ref(false)
const courseIntroduction = ref('')
const showFullDescription = ref(false)
const courseName = ref('情景课堂')

// 获取用户store
const userStore = useUserStore()

// 动态计算fab按钮位置
const fabGap = computed(() => ({
  top: 0,
  left: showPopup.value ? '66.67vw' : 0,
  right: 0,
  bottom: 200
}))

// 获取系统信息
const getSystemInfo = () => {
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
  
  // 计算标题栏顶部padding
  // 状态栏高度 + 额外间距
  headerPaddingTop.value = statusBarHeight.value + 50
}

// 显示问卷列表
const showSurveyList = () => {
  showPopup.value = true
  // 加载阶段数据、任务数据和问卷数据
  loadStageList()
  loadTaskList()
  loadSurveyList()
  loadCourseInfo()
}

// 关闭问卷列表
const closeSurveyList = () => {
  showPopup.value = false
}

// 处理弹窗关闭
const handlePopupClose = () => {
  showPopup.value = false
}

// 处理弹窗打开完成
const handlePopupOpened = () => {
  // 弹窗打开完成后的处理
  console.log('问卷列表弹窗已打开')
}

// 加载课程阶段列表
const loadStageList = async () => {
  if (!props.currentCourseId) {
    console.info('[SurveyList] 当前课程ID为空，无法获取阶段列表')
    return
  }
  
  isLoadingStages.value = true
  
  try {
    const response = await get(`/student/course/stage/list/${props.currentCourseId}`)
    console.log(`[SurveyList] 获取课程 ${props.currentCourseId} 阶段列表`)
    
    if (response.code === 200) {
      const stages = Array.isArray(response.data) ? response.data : []
      stageList.value = stages
      
      console.log(`[SurveyList] 成功获取阶段列表，共 ${stageList.value.length} 个阶段`)
    } else {
      console.error('[SurveyList] 获取阶段列表失败:', response)
      stageList.value = []
    }
  } catch (error) {
    console.error('[SurveyList] 获取阶段列表异常:', error)
    stageList.value = []
  } finally {
    isLoadingStages.value = false
  }
}

// 加载问卷列表
const loadSurveyList = async () => {
  if (!props.currentSceneId) {
    console.warn('[SurveyList] 当前场景ID为空，无法获取问卷列表')
    uni.showToast({
      title: '场景信息缺失',
      icon: 'none'
    })
    return
  }
  
  isLoading.value = true
  
  try {
    const response = await get(`/student/survey/scene/${props.currentSceneId}/list`)
    console.log(`[SurveyList] 获取场景 ${props.currentSceneId} 问卷列表`)
    
    if (response.code === 200) {
      const surveys = Array.isArray(response.data) ? response.data : []
      surveyList.value = surveys.map(item => ({
        ...item,
        paperTitle: item.PAPERTITLE || item.paperTitle,
        paperId: item.PAPERID || item.paperId,
        hasSubmitted: item.HASSUBMITTED === 1 || item.hasSubmitted === 1 || item.hasSubmitted === true
      }))
      
      console.log(`[SurveyList] 成功获取问卷列表，共 ${surveyList.value.length} 个问卷`)
    } else {
      console.error('[SurveyList] 获取问卷列表失败:', response)
      surveyList.value = []
      uni.showToast({
        title: response.msg || '获取问卷列表失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('[SurveyList] 获取问卷列表异常:', error)
    surveyList.value = []
    uni.showToast({
      title: '获取问卷列表异常',
      icon: 'error'
    })
  } finally {
    isLoading.value = false
  }
}

// 加载任务列表
const loadTaskList = async () => {
  if (!props.currentGroupId || props.currentGroupId === 'public') {
    console.info('[SurveyList] 当前分组ID为空或为公共聊天室，无法获取任务列表')
    taskList.value = []
    return
  }
  
  isLoadingTasks.value = true
  
  try {
    const response = await get(`/student/task/group/${props.currentGroupId}/list`)
    console.log(`[SurveyList] 获取分组 ${props.currentGroupId} 的任务列表`)
    
    if (response.code === 200) {
      const tasks = Array.isArray(response.data) ? response.data : []
      taskList.value = tasks.map(item => ({
        ...item,
        taskTitle: item.TASKTITLE,
        taskId: item.THID,
        thId: item.THID,
        statusDesc: item.STATUSDESC
      }))
      
      console.log(`[SurveyList] 成功获取任务列表，共 ${taskList.value.length} 个任务`)
    } else {
      console.error('[SurveyList] 获取任务列表失败:', response)
      taskList.value = []
      
      // 如果是认证相关错误，显示提示
      if (response.msg && response.msg.includes('登录')) {
        uni.showToast({
          title: response.msg,
          icon: 'none'
        })
      }
    }
  } catch (error) {
    console.error('[SurveyList] 获取任务列表异常:', error)
    taskList.value = []
  } finally {
    isLoadingTasks.value = false
  }
}

// 刷新问卷列表
const refreshSurveyList = async () => {
  if (isRefreshing.value || isLoading.value || isLoadingStages.value || isLoadingTasks.value) return // 防止重复点击
  
  isRefreshing.value = true
  
  try {
    // 同时刷新阶段列表、任务列表、问卷列表和课程信息
    await Promise.all([loadStageList(), loadTaskList(), loadSurveyList(), loadCourseInfo()])
    
    // 显示刷新完成提示
    uni.showToast({
      title: '刷新完成',
      icon: 'success',
      duration: 1500
    })
  } catch (error) {
    console.error('[SurveyList] 刷新列表失败:', error)
  } finally {
    // 延迟停止刷新动画，让用户看到旋转效果
    setTimeout(() => {
      isRefreshing.value = false
    }, 500)
  }
}

// 跳转到问卷页面
const navigateToSurvey = (paperId) => {
  console.log('[SurveyList] 跳转到问卷:', paperId)
  uni.navigateTo({
    url: `/pages/survey/index?paperId=${paperId}`
  })
}

// 组件挂载时获取系统信息
onMounted(() => {
  getSystemInfo()
})

// 跳转到任务页面
const navigateToTask = (thId) => {
  console.log('[SurveyList] 跳转到任务:', thId)
  
  if (!thId) {
    uni.showToast({
      title: '任务ID不能为空',
      icon: 'none'
    })
    return
  }
  
  // 跳转到任务详情页面
  uni.navigateTo({
    url: `/pages/task/detail?thId=${thId}`
  })
}

// 加载课程信息
const loadCourseInfo = async () => {
  try {
    const response = await get('/student/chat/currentUser/courseInfo')
    console.log('[SurveyList] 获取课程信息响应:', response)
    
    if (response.code === 200 && Array.isArray(response.data) && response.data.length > 0) {
      // 找到当前courseId对应的课程信息
      const currentCourseInfo = response.data.find(item => item.courseId === props.currentCourseId)
      if (currentCourseInfo) {
        if (currentCourseInfo.courseIntroduction) {
          courseIntroduction.value = currentCourseInfo.courseIntroduction
          console.log('[SurveyList] 成功获取课程介绍:', courseIntroduction.value)
        }
        if (currentCourseInfo.courseName) {
          courseName.value = currentCourseInfo.courseName
          console.log('[SurveyList] 成功获取课程名称:', courseName.value)
        }
      }
    }
  } catch (error) {
    console.error('[SurveyList] 获取课程信息异常:', error)
  }
}

// 切换描述显示状态
const toggleDescription = () => {
  if (courseIntroduction.value.length > 100) {
    if (showFullDescription.value) {
      showFullDescription.value = false
    } else {
      // 显示完整描述的弹窗
      uni.showModal({
        title: '课程介绍',
        content: courseIntroduction.value,
        showCancel: false,
        confirmText: '知道了'
      })
    }
  }
}

// 暴露方法给父组件
defineExpose({
  showSurveyList,
  closeSurveyList,
  refreshSurveyList,
  loadStageList,
  loadTaskList
})
</script>

<style scoped>
/* 悬浮按钮触发器样式 */
.survey-fab-trigger {
  width: 50px;
  height: 32px;
  background: #BD0407;
  border-radius: 0 25px 25px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(189, 4, 7, 0.4);
  transition: all 0.3s ease;
}

/* 弹窗容器 */
.survey-popup-container {
  height: 100vh;
  width: 81.67vw;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 0 16px 16px 0;
  box-shadow: 2px 0 12px rgba(0, 0, 0, 0.1);
}

/* 标题栏 */
.survey-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 12px;
  position: relative;
  transition: padding-top 0.3s ease;
}

.survey-title-wrapper {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: center;
}

.survey-title {
  font-size: 18px;
  font-weight: 600;
  margin-right: 8px;
}

.survey-count {
  font-size: 14px;
  color: #666;
}

.refresh-btn {
  padding: 8px;
  cursor: pointer;
  border-radius: 50%;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 刷新旋转动画 */
.refresh-btn.refreshing {
  animation: refresh-rotate 1s linear infinite;
}

@keyframes refresh-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 课程描述区域 */
.course-description-section {
  padding: 12px 24px 0;
}

.course-description {
  cursor: pointer;
  transition: background-color 0.2s ease;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.course-description:hover {
  background-color: #e9ecef;
}

.description-text {
  font-size: 14px;
  color: #555;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.course-description.expanded .description-text {
  -webkit-line-clamp: unset;
  overflow: visible;
}

/* 内容滚动区域 */
.survey-content-scroll {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  /* 自定义滚动条样式 */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: rgba(0, 0, 0, 0.3) transparent; /* Firefox */
}

/* Webkit浏览器的滚动条样式 */
.survey-content-scroll::-webkit-scrollbar {
  width: 4px;
}

.survey-content-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.survey-content-scroll::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 2px;
}

.survey-content-scroll::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.5);
}

/* 阶段列表容器 */
.stage-list-container {
  padding: 0 24px 12px;
}

.stage-timeline {
  position: relative;
}

.stage-item {
  display: flex;
  align-items: flex-start;
  position: relative;
}

.stage-item:last-child {
  margin-bottom: 0;
}

/* Timeline包装器 */
.timeline-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 16px;
  margin-top: 4px;
  z-index: 1;
}

/* Timeline圆点 */
.timeline-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ccc;
  border: 2px solid #fff;
  box-shadow: 0 0 0 1px #ccc;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.timeline-dot.active {
  background-color: #BD0407;
  box-shadow: 0 0 0 1px #BD0407;
}

/* Timeline连接线 */
.timeline-line {
  width: 1px;
  height: 21px;
  background-color: #e0e0e0;
  margin-top: 2px;
}

/* 阶段内容 */
.stage-content {
  flex: 1;
  padding-top: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stage-title {
  font-size: 15px;
  color: #333;
  font-weight: 500;
  line-height: 1.3;
  display: block;
  margin-bottom: 2px;
}

.stage-desc {
  font-size: 13px;
  color: #666;
  line-height: 1.3;
  display: block;
}

/* 新的分隔样式 */
.section-divider {
  height: 8px;
  background: linear-gradient(to right, transparent, #f0f0f0 20%, #f0f0f0 80%, transparent);
  margin: 12px 0;
}

/* 任务列表容器 */
.task-list-container {
  padding: 0 0 16px;
}

/* 任务项样式 */
.task-item {
  display: flex;
  align-items: center;
  padding: 12px 24px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.task-item:hover {
  background-color: #f8f8f8;
}

.task-item:last-child {
  border-bottom: none;
}

/* 已回复任务样式 */
.task-item.task-replied {
  background-color: transparent;
  border-left: none;
}

.task-item.task-replied:hover {
  background-color: #f8f8f8;
}

.task-item.task-replied .task-name {
  color: #333;
}

.task-item.task-replied .task-desc {
  color: #999;
  font-weight: normal;
}

/* 任务图标区域 */
.task-icon-wrapper {
  margin-right: 10px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 107, 53, 0.1);
  border-radius: 50%;
}

/* 任务信息 */
.task-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.task-name {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.task-desc {
  font-size: 12px;
  color: #999;
}

/* 任务状态区域 */
.task-status {
  margin-left: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 已回复图标 */
.replied-icon {
  animation: checkmark-bounce 0.6s ease-in-out;
}

@keyframes checkmark-bounce {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 锁定图标 */
.lock-icon {
  margin-right: 4px;
}

/* 问卷列表容器 */
.survey-list-container {
  /* 移除原来的滚动样式，现在由父容器统一处理 */
}

/* 问卷项样式 */
.survey-item {
  display: flex;
  align-items: center;
  padding: 12px 24px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f5f5f5;
}

.survey-item:hover {
  background-color: #f8f8f8;
}

.survey-item:last-child {
  border-bottom: none;
}

/* 问卷图标区域 */
.survey-icon-wrapper {
  margin-right: 10px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(189, 4, 7, 0.1);
  border-radius: 50%;
}

/* 问卷信息 */
.survey-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.survey-name {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.survey-desc {
  font-size: 12px;
  color: #999;
}

/* 问卷状态区域 */
.survey-status {
  margin-left: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 已提交图标 */
.submitted-icon {
  animation: checkmark-bounce 0.6s ease-in-out;
}

/* 空状态 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 16px;
}

.empty-text {
  font-size: 14px;
  color: #999;
}

/* 加载状态 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 16px;
}

.loading-text {
  font-size: 14px;
  color: #999;
}
</style> 